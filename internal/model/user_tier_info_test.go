package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestUserTierInfo_ShouldResetMonthly(t *testing.T) {
	t.Run("should return true when MonthlyResetAt is nil", func(t *testing.T) {
		tierInfo := &UserTierInfo{
			MonthlyResetAt: nil,
		}
		
		assert.True(t, tierInfo.ShouldResetMonthly())
	})

	t.Run("should return true when different month", func(t *testing.T) {
		lastMonth := time.Now().AddDate(0, -1, 0)
		tierInfo := &UserTierInfo{
			MonthlyResetAt: &lastMonth,
		}
		
		assert.True(t, tierInfo.ShouldResetMonthly())
	})

	t.Run("should return true when different year", func(t *testing.T) {
		lastYear := time.Now().AddDate(-1, 0, 0)
		tierInfo := &UserTierInfo{
			MonthlyResetAt: &lastYear,
		}
		
		assert.True(t, tierInfo.ShouldResetMonthly())
	})

	t.Run("should return false when same month and year", func(t *testing.T) {
		thisMonth := time.Now()
		tierInfo := &UserTierInfo{
			MonthlyResetAt: &thisMonth,
		}
		
		assert.False(t, tierInfo.ShouldResetMonthly())
	})
}

func TestUserTierInfo_ResetMonthlyStats(t *testing.T) {
	t.Run("should reset monthly stats correctly", func(t *testing.T) {
		tierInfo := &UserTierInfo{
			PointsThisMonth:     100,
			ActiveDaysThisMonth: 25,
			MonthlyResetAt:      nil,
		}

		tierInfo.ResetMonthlyStats()

		assert.Equal(t, 0, tierInfo.PointsThisMonth, "PointsThisMonth should be reset to 0")
		assert.Equal(t, 0, tierInfo.ActiveDaysThisMonth, "ActiveDaysThisMonth should be reset to 0")
		assert.NotNil(t, tierInfo.MonthlyResetAt, "MonthlyResetAt should be set")
		
		// Verify the reset date is recent (within last minute)
		now := time.Now()
		assert.WithinDuration(t, now, *tierInfo.MonthlyResetAt, time.Minute)
	})
}

func TestUserTierInfo_UpdateActivity(t *testing.T) {
	t.Run("should increment active days for new day", func(t *testing.T) {
		yesterday := time.Now().AddDate(0, 0, -1)
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 5,
			LastActivityDate:    &yesterday,
		}

		tierInfo.UpdateActivity()

		assert.Equal(t, 6, tierInfo.ActiveDaysThisMonth)
		assert.NotNil(t, tierInfo.LastActivityDate)
		
		// Should be today
		today := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Now().Location())
		activityDay := time.Date(tierInfo.LastActivityDate.Year(), tierInfo.LastActivityDate.Month(), tierInfo.LastActivityDate.Day(), 0, 0, 0, 0, tierInfo.LastActivityDate.Location())
		assert.True(t, activityDay.Equal(today) || activityDay.After(today))
	})

	t.Run("should not increment active days for same day", func(t *testing.T) {
		today := time.Now()
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 5,
			LastActivityDate:    &today,
		}

		tierInfo.UpdateActivity()

		assert.Equal(t, 5, tierInfo.ActiveDaysThisMonth, "Should not increment for same day")
	})

	t.Run("should increment active days when LastActivityDate is nil", func(t *testing.T) {
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 0,
			LastActivityDate:    nil,
		}

		tierInfo.UpdateActivity()

		assert.Equal(t, 1, tierInfo.ActiveDaysThisMonth)
		assert.NotNil(t, tierInfo.LastActivityDate)
	})
}

func TestUserTierInfo_MonthlyResetScenarios(t *testing.T) {
	t.Run("scenario: user with high active days from previous month", func(t *testing.T) {
		// Simulate user with 30 active days from last month
		lastMonth := time.Now().AddDate(0, -1, 0)
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 30, // High number from previous month
			PointsThisMonth:     500,
			MonthlyResetAt:      &lastMonth,
		}

		// Check if reset is needed
		assert.True(t, tierInfo.ShouldResetMonthly(), "Should need reset for previous month")

		// Perform reset
		tierInfo.ResetMonthlyStats()

		// Verify reset
		assert.Equal(t, 0, tierInfo.ActiveDaysThisMonth, "Should be reset to 0")
		assert.Equal(t, 0, tierInfo.PointsThisMonth, "Should be reset to 0")
		
		// Now update activity for today
		tierInfo.UpdateActivity()
		
		// Should have 1 active day for current month
		assert.Equal(t, 1, tierInfo.ActiveDaysThisMonth, "Should have 1 active day after reset and activity update")
	})

	t.Run("scenario: new user with nil MonthlyResetAt", func(t *testing.T) {
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 0,
			PointsThisMonth:     0,
			MonthlyResetAt:      nil, // New user
		}

		// Check if reset is needed
		assert.True(t, tierInfo.ShouldResetMonthly(), "Should need reset for nil MonthlyResetAt")

		// Perform reset
		tierInfo.ResetMonthlyStats()

		// Update activity
		tierInfo.UpdateActivity()

		// Should have 1 active day
		assert.Equal(t, 1, tierInfo.ActiveDaysThisMonth, "Should have 1 active day for new user")
		assert.NotNil(t, tierInfo.MonthlyResetAt, "MonthlyResetAt should be set")
	})

	t.Run("scenario: user active multiple times same day", func(t *testing.T) {
		tierInfo := &UserTierInfo{
			ActiveDaysThisMonth: 5,
			LastActivityDate:    nil,
		}

		// First activity of the day
		tierInfo.UpdateActivity()
		assert.Equal(t, 6, tierInfo.ActiveDaysThisMonth)

		// Second activity same day
		tierInfo.UpdateActivity()
		assert.Equal(t, 6, tierInfo.ActiveDaysThisMonth, "Should not increment again for same day")

		// Third activity same day
		tierInfo.UpdateActivity()
		assert.Equal(t, 6, tierInfo.ActiveDaysThisMonth, "Should still not increment for same day")
	})
}
