-- Debug script for activeLogonDays calculation issues
-- This script helps identify and fix problems with active days tracking

-- 1. Check current state of user_tier_info table
SELECT 
    user_id,
    active_days_this_month,
    last_activity_date,
    monthly_reset_at,
    created_at,
    updated_at,
    EXTRACT(MONTH FROM monthly_reset_at) as reset_month,
    EXTRACT(YEAR FROM monthly_reset_at) as reset_year,
    EXTRACT(MONTH FROM NOW()) as current_month,
    EXTRACT(YEAR FROM NOW()) as current_year,
    CASE 
        WHEN monthly_reset_at IS NULL THEN 'NEEDS_RESET'
        WHEN EXTRACT(MONTH FROM monthly_reset_at) != EXTRACT(MONTH FROM NOW()) THEN 'NEEDS_RESET'
        WHEN EXTRACT(YEAR FROM monthly_reset_at) != EXTRACT(YEAR FROM NOW()) THEN 'NEEDS_RESET'
        ELSE 'OK'
    END as reset_status
FROM user_tier_info 
ORDER BY updated_at DESC 
LIMIT 10;

-- 2. Find users with potentially incorrect active days (high numbers)
SELECT 
    user_id,
    active_days_this_month,
    monthly_reset_at,
    last_activity_date,
    CASE 
        WHEN active_days_this_month > 31 THEN 'SUSPICIOUS_HIGH'
        WHEN active_days_this_month > EXTRACT(DAY FROM NOW()) THEN 'HIGHER_THAN_CURRENT_DAY'
        ELSE 'NORMAL'
    END as status
FROM user_tier_info 
WHERE active_days_this_month > 0
ORDER BY active_days_this_month DESC;

-- 3. Check users who need monthly reset
SELECT 
    user_id,
    active_days_this_month,
    monthly_reset_at,
    'NEEDS_RESET' as action_needed
FROM user_tier_info 
WHERE monthly_reset_at < DATE_TRUNC('month', NOW()) 
   OR monthly_reset_at IS NULL;

-- 4. Simulate monthly reset for testing (BACKUP DATA FIRST!)
-- UNCOMMENT ONLY FOR TESTING:
/*
-- Backup current state
CREATE TABLE user_tier_info_backup AS 
SELECT * FROM user_tier_info WHERE user_id IN (
    SELECT user_id FROM user_tier_info 
    WHERE monthly_reset_at < DATE_TRUNC('month', NOW()) 
       OR monthly_reset_at IS NULL
    LIMIT 5
);

-- Reset monthly stats for testing users
UPDATE user_tier_info 
SET 
    active_days_this_month = 0,
    points_this_month = 0,
    monthly_reset_at = NOW(),
    updated_at = NOW()
WHERE user_id IN (
    SELECT user_id FROM user_tier_info_backup
);
*/

-- 5. Check for users with activity today
SELECT 
    user_id,
    active_days_this_month,
    last_activity_date,
    DATE(last_activity_date) as activity_date,
    DATE(NOW()) as today,
    CASE 
        WHEN DATE(last_activity_date) = DATE(NOW()) THEN 'ACTIVE_TODAY'
        ELSE 'NOT_ACTIVE_TODAY'
    END as today_status
FROM user_tier_info 
WHERE last_activity_date IS NOT NULL
ORDER BY last_activity_date DESC
LIMIT 10;

-- 6. Monthly statistics summary
SELECT 
    EXTRACT(MONTH FROM monthly_reset_at) as month,
    EXTRACT(YEAR FROM monthly_reset_at) as year,
    COUNT(*) as user_count,
    AVG(active_days_this_month) as avg_active_days,
    MAX(active_days_this_month) as max_active_days,
    MIN(active_days_this_month) as min_active_days
FROM user_tier_info 
WHERE monthly_reset_at IS NOT NULL
GROUP BY EXTRACT(MONTH FROM monthly_reset_at), EXTRACT(YEAR FROM monthly_reset_at)
ORDER BY year DESC, month DESC;

-- 7. Find users with inconsistent data
SELECT 
    user_id,
    active_days_this_month,
    last_activity_date,
    monthly_reset_at,
    'INCONSISTENT' as issue_type,
    CASE 
        WHEN last_activity_date < monthly_reset_at AND active_days_this_month > 0 THEN 'ACTIVITY_BEFORE_RESET'
        WHEN monthly_reset_at IS NULL AND active_days_this_month = 0 THEN 'NULL_RESET_ZERO_DAYS'
        WHEN EXTRACT(MONTH FROM last_activity_date) != EXTRACT(MONTH FROM NOW()) AND active_days_this_month > 0 THEN 'OLD_ACTIVITY_CURRENT_DAYS'
        ELSE 'OTHER'
    END as issue_detail
FROM user_tier_info 
WHERE (
    (last_activity_date < monthly_reset_at AND active_days_this_month > 0)
    OR (monthly_reset_at IS NULL AND created_at < DATE_TRUNC('month', NOW()))
    OR (EXTRACT(MONTH FROM last_activity_date) != EXTRACT(MONTH FROM NOW()) AND active_days_this_month > 0)
);

-- 8. Test query to verify the repository logic
-- This matches the repository query: GetUsersNeedingMonthlyReset
SELECT 
    user_id,
    active_days_this_month,
    monthly_reset_at,
    'REPOSITORY_MATCH' as source
FROM user_tier_info 
WHERE monthly_reset_at < DATE_TRUNC('month', NOW()) 
   OR monthly_reset_at IS NULL;

-- 9. Performance check - ensure indexes exist
EXPLAIN (ANALYZE, BUFFERS) 
SELECT user_id, active_days_this_month 
FROM user_tier_info 
WHERE monthly_reset_at < DATE_TRUNC('month', NOW()) 
   OR monthly_reset_at IS NULL;

-- 10. Recommended indexes (if not already present)
-- CREATE INDEX CONCURRENTLY idx_user_tier_info_monthly_reset_at ON user_tier_info(monthly_reset_at);
-- CREATE INDEX CONCURRENTLY idx_user_tier_info_last_activity_date ON user_tier_info(last_activity_date);
-- CREATE INDEX CONCURRENTLY idx_user_tier_info_user_id_monthly_reset ON user_tier_info(user_id, monthly_reset_at);
